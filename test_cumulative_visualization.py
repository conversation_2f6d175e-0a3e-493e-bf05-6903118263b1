#!/usr/bin/env python3
"""
测试修改后的累积密度可视化功能
"""

import sys
import numpy as np
from pathlib import Path

# 添加当前目录到路径
sys.path.append('.')

try:
    # 导入修改后的可视化器
    import importlib.util
    spec = importlib.util.spec_from_file_location("ship_domain_visualization", "15.ship_domain_visualization.py")
    vis_module = importlib.util.module_from_spec(spec)
    spec.loader.exec_module(vis_module)
    ShipDomainVisualizer = vis_module.ShipDomainVisualizer
    
    print("✅ 成功导入 ShipDomainVisualizer")
    
    # 创建可视化器实例
    visualizer = ShipDomainVisualizer(debug=True)
    
    print("✅ 成功创建可视化器实例")
    
    # 测试数据加载
    print("\n=== 测试数据加载 ===")
    
    # 检查必要的数据文件是否存在
    density_file = Path("result/probability_density/sector_boundaries_results.pkl")
    ellipse_file = Path("result/ship_domain_fitting/ship_domain_ellipse_params.pkl")
    
    print(f"概率密度结果文件: {'✅' if density_file.exists() else '❌'} {density_file}")
    print(f"椭圆拟合结果文件: {'✅' if ellipse_file.exists() else '❌'} {ellipse_file}")
    
    if not density_file.exists():
        print("\n⚠️ 缺少概率密度分析结果文件")
        print("请先运行: python 13.probability_density_analysis.py")
        
    if not ellipse_file.exists():
        print("\n⚠️ 缺少椭圆拟合结果文件")
        print("请先运行: python 14.ship_domain_fitting.py")
    
    if density_file.exists() and ellipse_file.exists():
        # 尝试加载数据
        success = visualizer.load_data()
        
        if success:
            print("✅ 数据加载成功")
            
            # 列出可用场景
            print("\n=== 可用场景列表 ===")
            scenes = visualizer.list_available_scenes()
            
            if scenes:
                # 测试单个场景的可视化
                test_scene = scenes[0]  # 使用第一个场景进行测试
                print(f"\n=== 测试场景可视化: {test_scene} ===")
                
                # 检查该场景是否有累积密度数据
                if test_scene in visualizer.density_results:
                    density_data = visualizer.density_results[test_scene]
                    sector_analysis = density_data.get('sector_analysis', {})
                    
                    cumulative_data_count = 0
                    for sector_name, sector_data in sector_analysis.items():
                        if (sector_data is not None and 
                            sector_data.get('boundary_info') is not None and
                            'cumulative_densities' in sector_data['boundary_info']):
                            cumulative_densities = sector_data['boundary_info']['cumulative_densities']
                            if cumulative_densities:
                                cumulative_data_count += 1
                                print(f"  {sector_name}: {len(cumulative_densities)} 个累积密度点")
                    
                    if cumulative_data_count > 0:
                        print(f"✅ 找到 {cumulative_data_count} 个扇区的累积密度数据")
                        
                        # 测试步骤3的可视化
                        print("\n=== 测试步骤3累积密度可视化 ===")
                        try:
                            success = visualizer.create_single_visualization(test_scene, step=3)
                            if success:
                                print("✅ 步骤3累积密度可视化生成成功")
                                print("📁 输出目录: vis/single_visualization/")
                            else:
                                print("❌ 步骤3可视化生成失败")
                        except Exception as e:
                            print(f"❌ 步骤3可视化测试失败: {e}")
                            import traceback
                            traceback.print_exc()
                    else:
                        print("⚠️ 该场景没有累积密度数据")
                        print("请确保使用修改后的 13.probability_density_analysis.py 重新生成数据")
                else:
                    print(f"❌ 场景 {test_scene} 的密度数据不存在")
            else:
                print("❌ 没有可用的场景数据")
        else:
            print("❌ 数据加载失败")
    else:
        print("\n⚠️ 缺少必要的数据文件，跳过可视化测试")
    
    print("\n✅ 累积密度可视化功能测试完成")
    
except ImportError as e:
    print(f"❌ 导入失败: {e}")
    sys.exit(1)
except Exception as e:
    print(f"❌ 测试失败: {e}")
    import traceback
    traceback.print_exc()
    sys.exit(1)

print("\n🎉 所有测试通过！")
print("\n💡 使用提示:")
print("1. 确保先运行修改后的 13.probability_density_analysis.py 生成累积密度数据")
print("2. 运行 14.ship_domain_fitting.py 生成椭圆拟合数据")
print("3. 运行 15.ship_domain_visualization.py 生成包含累积密度的可视化图表")
print("4. 新的步骤3将显示累积密度随距离变化的曲线，红色点标记密度变化率最大的边界点")
